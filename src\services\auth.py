from src.utils.encryption import EncryptionUtility
import traceback
import uuid
from enum import Enum
from uuid import UUID
from datetime import timed<PERSON><PERSON>, datetime, timezone
import random

from sqlalchemy import delete, select, cast
from sqlalchemy.orm import selectinload
from sqlalchemy import or_, any_
from sqlalchemy.types import Enum as PgEnum



from user_agents import parse
from fastapi import HTTPException, Response, Request, status, UploadFile
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Union

import src.schemas
from src.DB import UserAuth
from src.DB.enums import AuthProvider, UserRole
from src.DB.enums import DeviceType
from src.schemas.auth import TokenData, AccessTokenPayload, RefreshTokenPayload
from src.schemas.user import SocialLoginRequest,UserRead
from src.utils.oauth_verification import verify_facebook_token, verify_google_token
from src.utils.user_utils import generate_pass_hash, verify_hash_pass, create_jwt_at_token, \
    create_jwt_rt_token, set_refresh_token_in_cookie
from src.DB.models.users import User, RefreshToken
from src.schemas import auth as auth_schema
from src.schemas import user as user_schema
from config import settings
from src.utils.Email.email import Email
from src.utils.Error_Handling import ErrorCode
from src.services.auth_helper import (create_verification_code_general,
check_verification_code_general, verify_recaptcha, get_public_ip, 
generate_complex_password
)
from src.utils.logger import AdvancedLogger

logger = AdvancedLogger(name=__name__)

ACCESS_TOKEN_EXPIRY = settings.ACCESS_TOKEN_EXPIRY
REFRESH_TOKEN_EXPIRY_PC = settings.REFRESH_TOKEN_EXPIRY_PC
REFRESH_TOKEN_EXPIRY_MO = settings.REFRESH_TOKEN_EXPIRY_MO
LOCKOUT_DURATION_MINS = settings.LOCKOUT_DURATION_MINS
MAX_LOGIN_ATTEMPTS = settings.MAX_LOGIN_ATTEMPTS


encryption_utility = EncryptionUtility()

async def create_user(user_data: user_schema.UserCreate, db: AsyncSession) -> User:
    try:

        await verify_recaptcha(user_data.recaptcha_token)

        user_id = uuid.uuid4()

        new_user = User(
            user_id=user_id,
            name=user_data.name,
            email=user_data.email,
            user_name=user_data.user_name,
            profile_picture=None,
            date_of_birth=user_data.date_of_birth,
            gender=user_data.gender,
            country_id=user_data.country_id,
        )
        db.add(new_user)
        
        if user_data.password:
            complicated_password = user_data.password
        else:
            complicated_password = str(generate_complex_password(15))
            logger.info(f"complicated_password: {complicated_password}")

        random_number = str(random.randint(100000, 999999))
        pass_hash = generate_pass_hash(complicated_password)
        ex_vc_date = datetime.now(tz=timezone.utc) + timedelta(minutes=20)

        new_user_auth = UserAuth(
            user_id=user_id,  # Use the same user_id
            hashed_password=pass_hash,
            verification_code=random_number,
            email_confirmed=True,  # Set email_confirmed to True for admin users.
            verification_code_exp=ex_vc_date,
            created_at=datetime.now(tz=timezone.utc),
            updated_at=datetime.now(tz=timezone.utc),
        )
        db.add(new_user_auth)
        await db.commit()
        await db.refresh(new_user)

        # Ensure relationship is explicitly set
        new_user.auth = new_user_auth
        # await create_verification_code_general(new_user, db)

        ############ Send Email ################
        user_read = UserRead.model_validate(new_user)
        email_service = Email(user_read)
        email_service.send_registration_email(new_user_auth.verification_code)
        ########################################

        return new_user

    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500, detail=ErrorCode.UNEXPECTED_ERROR)

async def login_user(request: Request, response: Response, form_data, db: AsyncSession) -> TokenData:
    try:
        email = form_data.username.strip().lower()
        password = form_data.password
        

        user = await get_user_by_email_or_username_for_login(db, email)
        if user is None:
            raise HTTPException(
                status_code=404, detail=ErrorCode.LOGIN_INVALID_ERROR)

        if user.auth is None:
            raise HTTPException(
                status_code=404, detail=ErrorCode.LOGIN_INVALID_ERROR)

        current_time = datetime.now(tz=timezone.utc)

        # TODO: how are you implementing Google or FB registration and authenticating?
        if user.auth.auth_provider == AuthProvider.LOCAL:
            lockout_until = user.auth.lockout_until
            if user.auth.lockout_until is not None and user.auth.lockout_until > current_time:
                remaining_minutes = int(
                    (user.auth.lockout_until - current_time).total_seconds() // 60)
                await db.commit()
                raise HTTPException(
                    status_code=403,
                    detail=ErrorCode.ACCOUNT_LOCKED.format(
                        minutes=remaining_minutes)
                )

            if user.auth.lockout_until and lockout_until < current_time:
                user.auth.failed_login_attempts = 0
                user.auth.lockout_until = None

            if user.auth.hashed_password is None or not verify_hash_pass(password, user.auth.hashed_password):
                user.auth.failed_login_attempts += 1
                if user.auth.failed_login_attempts >= MAX_LOGIN_ATTEMPTS:
                    user.auth.lockout_until = current_time + \
                        timedelta(minutes=LOCKOUT_DURATION_MINS)
                    await db.commit()
                    raise HTTPException(
                        status_code=403,
                        detail=ErrorCode.ACCOUNT_LOCKED_MINUTES
                    )
                await db.commit()
                raise HTTPException(
                    status_code=400, detail=ErrorCode.LOGIN_INVALID_password_ERROR.value)

        if not user.auth.email_confirmed:
            await create_verification_code_general(user, db)
            ############ Send Email ################
            user_read = UserRead.model_validate(user)
            email_service = Email(user_read)
            email_service.send_registration_email(user.auth.verification_code)
            ########################################

            raise HTTPException(
                status_code=403, detail=ErrorCode.EMAIL_NOT_CONFIRM.value)


        user.auth.failed_login_attempts = 0
        user.auth.lockout_until = None
        user.last_login = current_time
        if not user.is_active:
            user.is_active == True
        
        if user.is_deleted:
            user.is_deleted = False

        user_agent_string = request.headers.get("User-Agent", "Unknown")
        user_agent = parse(user_agent_string)
        device_type = DeviceType.MOBILE if user_agent.is_mobile else DeviceType.PC if user_agent.is_pc else DeviceType.UNKNOWN

        public_ip = await get_public_ip() or request.client.host

        jwt_id = uuid.uuid4()
        at_payload = AccessTokenPayload(user_id=str(user.user_id))
        rt_payload = RefreshTokenPayload(
            user_id=str(user.user_id),
            jwt_id=str(jwt_id),
            device_type=device_type.value
        )

        access_token = create_jwt_at_token(at_payload)
        new_refresh_token = create_jwt_rt_token(
            data=rt_payload, device_type=device_type)

        encrypted_refresh_token = encryption_utility.encrypt(
            new_refresh_token.jwt)

        await db.execute(delete(RefreshToken).where(
            RefreshToken.user_id == user.user_id,
            RefreshToken.device_type == device_type.value
        ))

        new_refresh_token_db = RefreshToken(
            jwt_id=jwt_id,
            user_id=user.user_id,
            hash_refresh_token=encrypted_refresh_token,
            public_ip=public_ip,
            device_type=device_type.value,
            refresh_token_exp=new_refresh_token.exp,
        )
        db.add(new_refresh_token_db)
        await db.commit()

        set_refresh_token_in_cookie(response=response, device_type=DeviceType(
            new_refresh_token_db.device_type), refresh_token=new_refresh_token.jwt)

        return TokenData(access_token=access_token, user_name=user.user_name, roles=user.roles)

    except HTTPException as e:
        await db.rollback()
        raise e
    except Exception as e:
        await db.rollback()
        if settings.ENVIRONMENT == 'local':
            traceback.print_exc()
            raise HTTPException(
                status_code=500, detail=ErrorCode.UNEXPECTED_ERROR)
        else:
            raise HTTPException(
                status_code=500, detail=ErrorCode.UNEXPECTED_ERROR)

async def confirm_email(user: User, code: auth_schema.RegistrationConfirmation, db: AsyncSession) -> bool:
    try:
        await verify_recaptcha(code.recaptcha_token)
        await check_verification_code_general(user, code.verificationCode)
        user.auth.verification_code = None
        user.auth.verification_code_exp = None
        user.auth.email_confirmed = True

        # user_read = UserRead.model_validate(user)
        # email_service = Email(user_read)
        # email_service.send_complete_verification_email()

        user.is_created = True
        await db.commit()
        await db.refresh(user)

        return True
    except HTTPException as e:
        await db.rollback()
        raise e
    except Exception as e:
        await db.rollback()
        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail=ErrorCode.UNEXPECTED_ERROR)

async def update_email(user: User, update_data: auth_schema.EmailUpdateRequest, db: AsyncSession) -> User:
    try:
        await verify_recaptcha(update_data.recaptcha_token)
        await check_verification_code_general(user, update_data.verificationCode)

        # Send confirmation email
        # user_read = UserRead.model_validate(user)
        # email_service = Email(user_read)
        # email_service.send_email_changed_email(update_data.new_email)

        # TODO:We need to add confirm here,i.e. we need to send new code to varify the new E-mail and then he can use 
        # Update local user
        user.email = update_data.new_email
        user.auth.verification_code = None
        user.auth.verification_code_exp = None
        await db.commit()
        await db.refresh(user)

        # Update microservices
        update_payload = auth_schema.UserUpdate(email=update_data.new_email)

        return user

    except HTTPException as e:
        await db.rollback()
        raise e
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=ErrorCode.UNEXPECTED_ERROR
        )

async def send_email_with_vc_for_rest_password(user: User, db: AsyncSession, request: Request) -> bool:
    await create_verification_code_general(user, db)
    # ########### Send WhatsApp message #############
    user_read = UserRead.model_validate(user)
    email_service = Email(user_read)
    email_service.send_password_reset_email(user.auth.verification_code)

    return True

async def user_change_password(user: User, user_data: auth_schema.NewPassword, db: AsyncSession):
    try:
        await verify_recaptcha(user_data.recaptcha_token)
        if user_data.new_password != user_data.confirm_password:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail=ErrorCode.PASSWORDS_DONT_MATCH)

        if not verify_hash_pass(user_data.old_password, user.auth.hashed_password):
            raise HTTPException(
                status_code=400, detail=ErrorCode.OLD_PASSWORD_INCORRECT)
        user.auth.hashed_password = generate_pass_hash(user_data.new_password)
        await db.commit()
        await db.refresh(user)

        ############ Send Email ################
        # user_read = UserRead.model_validate(user)
        # email_service = Email(user_read)
        # email_service.send_password_changed_email()
        ########################################

    except HTTPException as http_exc:
        raise http_exc
    except Exception:
        await db.rollback()
        raise HTTPException(
            status_code=500, detail=ErrorCode.PASSWORD_CHANGE_ERROR)

# async def upload_profile_picture_helper(db: AsyncSession, user_id: UUID, profile_picture: UploadFile) -> str:
#     try:
#         user = await get_user_by_id(db, user_id)

#         file_manager = FilesUtils(
#             file_type=MongoDBChatMessageType.image,
#             file=profile_picture,
#             user=src.schemas.user.UserRead.model_validate(user),
#             file_source=CloudFlareFileSource.USER_PROFILE,
#             bucket=CloudFlareR2Buckets.PUBLIC,
#         )
#         if settings.ENVIRONMENT == "development":
#             obj_name = f"dev/{str(user_id)}/public/profile/{uuid.uuid4()}"
#         elif settings.ENVIRONMENT == "local":
#             obj_name = f"loc/{str(user_id)}/public/profile/{uuid.uuid4()}"
#         else:
#             obj_name = f"prod/{str(user_id)}/public/profile/{uuid.uuid4()}"
#         # /2912bb20-ec16-4f14-b679-56bc60970190/public/identity-service/2912bb20-ec16-4f14-b679-56bc60970190.png
#         if user.profile_picture:
#             old_url = user.profile_picture
#             await file_manager.delete_file(object_name=old_url)

#         image_object_name = await file_manager.store_public_image_and_get_object_name(object_name=obj_name)
#         profile_picture_url = f"{image_object_name}"

#         user.profile_picture = profile_picture_url
#         await db.commit()
#         await db.refresh(user)

#         # Update microservices
#         update_payload = auth_schema.UserUpdate(
#             profile_picture=str(profile_picture_url))

#         return f"{settings.CDN_HOST}/{image_object_name}"
#     except HTTPException as e:
#         traceback.print_exc()
#         raise HTTPException(status_code=500, detail=str(e))
#     except Exception as e:
#         traceback.print_exc()
#         raise HTTPException(
#             status_code=500, detail=f"Error uploading profile picture: {str(e)}")

#     #########################################################################

async def update_profile(user: User, update_data: Union[dict, auth_schema.UserUpdate], db: AsyncSession):
    # Handle both dict and Pydantic model inputs
    if isinstance(update_data, dict):
        data = update_data
    else:
        data = update_data.model_dump(exclude_unset=True)

    for key, value in data.items():
        # Store Enum values as strings in the model
        if isinstance(value, Enum):
            value = value.value
        setattr(user, key, value)

    await db.commit()
    await db.refresh(user)

    return user

#########################

async def get_user_by_email_or_username_for_login(db: AsyncSession, email: str) -> Optional[User]:
    result = await db.execute(
        select(User)
        .options(selectinload(User.auth))
        .where(
            or_(
                User.email == email,
                User.user_name == email,
            ),
            User.is_banned == False
        )
    )
    return result.scalar_one_or_none()

async def get_user_by_email_or_username(db: AsyncSession, email: str) -> Optional[User]:
    result = await db.execute(
        select(User)
        .options(selectinload(User.auth))
        .where(
            or_(
                User.email == email,
                User.user_name == email,
            ),
            User.is_banned == False,
            User.is_active == True,
            User.is_deleted == False
        )
    )
    return result.scalar_one_or_none()

async def get_user_by_id(db: AsyncSession, user_id: UUID) -> Optional[User]:
    result = await db.execute(
        select(User)
        .options(selectinload(User.auth))
        .where(user_id == User.user_id, User.is_active == True, User.is_deleted == False, User.is_banned == False)
    )
    user = result.scalars().first()
    return user

async def get_users(db: AsyncSession, skip: int, limit: int):
    stmt = select(User).offset(skip).limit(limit)
    result = await db.execute(stmt)
    return result.scalars().all()

async def get_user(db: AsyncSession, user_id: str) -> Optional[User]:
    stmt = select(User).where(User.user_id == user_id)
    result = await db.execute(stmt)
    return result.scalars().first()

async def get_user_roles(db: AsyncSession, user_id: str) -> Optional[User]:
    stmt = select(User).where(
        User.user_id == user_id,
        cast(UserRole.ADMIN.value, PgEnum(UserRole, name="userrole")) == any_(User.roles)
    )
    result = await db.execute(stmt)
    return result.scalars().first()

################################

async def social_login(request: Request, payload: SocialLoginRequest,
                       response: Response, db: AsyncSession) -> TokenData:
    try:
        provider = payload.provider
        access_token = payload.access_token
        user_info = None

        if provider == AuthProvider.GOOGLE:
            user_info = await verify_google_token(access_token)
            if not user_info:
                raise HTTPException(
                    status_code=400, detail=ErrorCode.INVALID_GOOGLE_TOKEN)

        if provider == AuthProvider.FACEBOOK:
            user_info = await verify_facebook_token(access_token)
            if not user_info:
                raise HTTPException(
                    status_code=400, detail=ErrorCode.INVALID_FACEBOOK_TOKEN)

        if not user_info or not user_info.get("email"):
            raise HTTPException(status_code=400, detail=ErrorCode.MISSING_USER_INFO)

        # Check if user exists
        user = await get_user_by_email_or_username_for_login(db, user_info["email"])

        if not user:
            # Register user (auto)
            user_id = uuid.uuid4()
            hash_user_id = encryption_utility.encrypt(str(user_id))

            user = User(
                user_id=user_id,
                user_id_hash=hash_user_id,
                email=user_info["email"],
                name=user_info["name"],
                profile_picture=user_info["profile_picture"],
                roles=[UserRole.User],
                is_active=True,
            )
            db.add(user)

            auth = UserAuth(
                user_id=user_id,
                auth_provider=AuthProvider(provider.value),
                email_confirmed=True
            )
            db.add(auth)
            await db.commit()
            await db.refresh(user)

        # Return JWT
        user.auth.failed_login_attempts = 0
        user.auth.lockout_until = None
        current_time = datetime.now(tz=timezone.utc)
        user.last_login = current_time

        user_agent_string = request.headers.get("User-Agent", "Unknown")
        user_agent = parse(user_agent_string)
        device_type = DeviceType.MOBILE.value if user_agent.is_mobile else DeviceType.PC.value if user_agent.is_pc else DeviceType.UNKNOWN.value

        public_ip = await get_public_ip() or request.client.host

        jwt_id = uuid.uuid4()
        at_payload = AccessTokenPayload(user_id=str(user.user_id))
        rt_payload = RefreshTokenPayload(
            user_id=str(user.user_id),
            jwt_id=str(jwt_id),
            device_type=device_type
        )

        access_token = create_jwt_at_token(at_payload)
        new_refresh_token = create_jwt_rt_token(
            data=rt_payload, device_type=device_type)

        encrypted_refresh_token = encryption_utility.encrypt(
            new_refresh_token.jwt)

        await db.execute(delete(RefreshToken).where(
            RefreshToken.user_id == user.user_id,
            RefreshToken.device_type == device_type
        ))

        new_refresh_token_db = RefreshToken(
            jwt_id=jwt_id,
            user_id=user.user_id,
            hash_refresh_token=encrypted_refresh_token,
            public_ip=public_ip,
            device_type=device_type,
            refresh_token_exp=new_refresh_token.exp,
        )
        db.add(new_refresh_token_db)
        await db.commit()

        set_refresh_token_in_cookie(response=response, device_type=DeviceType(new_refresh_token_db.device_type),
                                    refresh_token=new_refresh_token.jwt)

        return TokenData(access_token=access_token)
    except HTTPException as e:
        await db.rollback()
        traceback.print_exc()
        raise e
    except Exception as e:
        await db.rollback()
        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail=ErrorCode.UNEXPECTED_ERROR)
        
async def create_user_by_admin(user_data: user_schema.UserCreateAdmin, db: AsyncSession) -> User:
    try:

        await verify_recaptcha(user_data.recaptcha_token)

        user_id = uuid.uuid4()

        new_user = User(
            user_id=user_id,
            name=user_data.name,
            email=user_data.email,
            user_name=user_data.user_name,
            profile_picture=None,
            date_of_birth=user_data.date_of_birth,
            gender=user_data.gender,
            country_id=user_data.country_id,
            roles=user_data.roles,
        )
        db.add(new_user)
        
        complicated_password = str(generate_complex_password(15))
        # logger.info(f"complicated_password: {complicated_password}")

        random_number = str(random.randint(100000, 999999))
        pass_hash = generate_pass_hash(complicated_password)
        ex_vc_date = datetime.now(tz=timezone.utc) + timedelta(minutes=20)

        new_user_auth = UserAuth(
            user_id=user_id,  # Use the same user_id
            hashed_password=pass_hash,
            verification_code=random_number,
            verification_code_exp=ex_vc_date,
            email_confirmed=True,  # Set email_confirmed to True for admin users.
            created_at=datetime.now(tz=timezone.utc),
            updated_at=datetime.now(tz=timezone.utc),
        )
        db.add(new_user_auth)
        await db.commit()
        await db.refresh(new_user)

        # Ensure relationship is explicitly set
        new_user.auth = new_user_auth
        # await create_verification_code_general(new_user, db)

        ############ Send Email ################
        user_read = UserRead.model_validate(new_user)
        email_service = Email(user_read)
        email_service.send_registration_email_for_admin_user(password=complicated_password)
        ########################################

        return new_user

    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500, detail=ErrorCode.UNEXPECTED_ERROR)

async def update_status(user: User, db: AsyncSession):
    user.is_active = True
    ############ Send Email ################
    # user_read = UserRead.model_validate(user)
    # email_service = Email(user_read)
    # email_service.send_deactivation_email()
    ########################################
    await db.commit()
    await db.refresh(user)


async def ban_user(user: User, db: AsyncSession):
    user.is_banned != user.is_banned
    ############ Send Email ################
    # user_read = UserRead.model_validate(user)
    # email_service = Email(user_read)
    # email_service.send_deactivation_email()
    ########################################
    await db.commit()
    await db.refresh(user)


async def user_verification_code(user: User, user_data: auth_schema.PasswordResetRequest, db: AsyncSession):
    try:
        await check_verification_code_general(user, user_data.verificationCode)

        if user_data.new_password != user_data.confirm_password:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=ErrorCode.PASSWORDS_DONT_MATCH)
        if verify_hash_pass(user_data.old_password, user.auth.hashed_password):
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=ErrorCode.OLD_PASSWORD_INCORRECT)
        user.auth.email_confirmed = True
        user.auth.hashed_password = generate_pass_hash(user_data.new_password)
        user.auth.verification_code = None
        user.auth.verification_code_exp = None


        await db.commit()
        await db.refresh(user)

    except HTTPException as e:
        await db.rollback()
        raise e
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"{ErrorCode.UNEXPECTED_ERROR}: {str(e)}")