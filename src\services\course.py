from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from fastapi import HTTPException
import uuid
from uuid import UUID
from datetime import datetime, timezone
from src.DB.models.assessments import Course, Lesson, CourseContent, UserLessonProgress
from src.schemas.course import CourseCreate, CourseUpdate, LessonCreate, LessonUpdate, CourseContentCreate, CourseContentUpdate, LessonProgressUpdate
from src.utils.Error_Handling import ErrorCode
from typing import List

# --------------------
# Course Services
# --------------------


async def create_course(data: CourseCreate, db: AsyncSession):
    new_course = Course(
        course_id=uuid.uuid4(),
        title=data.title,
        description=data.description,
        is_active=data.is_active
    )
    db.add(new_course)
    await db.commit()
    await db.refresh(new_course)
    return new_course


async def get_all_courses(db: AsyncSession):
    result = await db.execute(select(Course).options(selectinload(Course.contents)))
    return result.scalars().all()


async def get_course_by_id(course_id: UUID, db: AsyncSession):
    result = await db.execute(
        select(Course)
        .options(selectinload(Course.contents).selectinload(CourseContent.lesson),
                 selectinload(Course.contents).selectinload(CourseContent.assessment))
        .where(Course.course_id == course_id)
    )
    return result.scalar_one_or_none()


async def update_course(course_id: UUID, data: CourseUpdate, db: AsyncSession):
    course = await get_course_by_id(course_id, db)
    if not course:
        raise HTTPException(status_code=404, detail=ErrorCode.COURSE_NOT_FOUND)
    for k, v in data.model_dump(exclude_unset=True).items():
        setattr(course, k, v)
    await db.commit()
    await db.refresh(course)
    return course


async def delete_course(course_id: UUID, db: AsyncSession):
    course = await get_course_by_id(course_id, db)
    if not course:
        return False
    await db.delete(course)
    await db.commit()
    return True


# --------------------
# Lesson Services
# --------------------

async def create_lesson(course_id: UUID, data: LessonCreate, db: AsyncSession):
    new_lesson = Lesson(
        lesson_id=uuid.uuid4(),
        title=data.title,
        body_md=data.body_md,
        video_url=data.video_url,
        file_url=data.file_url
    )
    db.add(new_lesson)
    await db.commit()
    await db.refresh(new_lesson)

    # attach to course as content
    content = CourseContent(
        content_id=uuid.uuid4(),
        course_id=course_id,
        content_type=data.content_type,
        lesson_id=new_lesson.lesson_id,
        sequence=0
    )
    db.add(content)
    await db.commit()
    return new_lesson


async def get_lessons_by_course(course_id: UUID, db: AsyncSession):
    result = await db.execute(
        select(Lesson).join(CourseContent).where(
            CourseContent.course_id == course_id)
    )
    return result.scalars().all()


async def get_lesson_by_id(lesson_id: UUID, db: AsyncSession):
    result = await db.execute(select(Lesson).where(Lesson.lesson_id == lesson_id))
    return result.scalar_one_or_none()


async def update_lesson(lesson_id: UUID, data: LessonUpdate, db: AsyncSession):
    lesson = await get_lesson_by_id(lesson_id, db)
    if not lesson:
        raise HTTPException(status_code=404, detail=ErrorCode.LESSON_NOT_FOUND)
    for k, v in data.model_dump(exclude_unset=True).items():
        setattr(lesson, k, v)
    await db.commit()
    await db.refresh(lesson)
    return lesson


async def delete_lesson(lesson_id: UUID, db: AsyncSession):
    lesson = await get_lesson_by_id(lesson_id, db)
    if not lesson:
        return False
    await db.delete(lesson)
    await db.commit()
    return True


async def bulk_create_lessons(course_id: UUID, lessons: List[LessonCreate], db: AsyncSession):
    created = []
    for lesson_data in lessons:
        new_lesson = Lesson(
            lesson_id=uuid.uuid4(),
            title=lesson_data.title,
            body_md=lesson_data.body_md,
            video_url=lesson_data.video_url,
            file_url=lesson_data.file_url
        )
        db.add(new_lesson)
        await db.flush()  # get lesson_id before commit

        content = CourseContent(
            content_id=uuid.uuid4(),
            course_id=course_id,
            content_type="lesson",
            lesson_id=new_lesson.lesson_id,
            sequence=0
        )
        db.add(content)
        created.append(new_lesson)

    await db.commit()
    for l in created:
        await db.refresh(l)
    return created

# --------------------
# Course Content Services
# --------------------


async def add_course_content(course_id: UUID, data: CourseContentCreate, db: AsyncSession):
    content = CourseContent(
        content_id=uuid.uuid4(),
        course_id=course_id,
        content_type=data.content_type,
        lesson_id=data.lesson_id,
        assessment_id=data.assessment_id,
        sequence=data.sequence
    )
    db.add(content)
    await db.commit()
    await db.refresh(content)
    return content


async def update_course_content(content_id: UUID, data: CourseContentUpdate, db: AsyncSession):
    result = await db.execute(select(CourseContent).where(CourseContent.content_id == content_id))
    content = result.scalar_one_or_none()
    if not content:
        raise HTTPException(
            status_code=404, detail=ErrorCode.CONTENT_NOT_FOUND)
    for k, v in data.model_dump(exclude_unset=True).items():
        setattr(content, k, v)
    await db.commit()
    await db.refresh(content)
    return content


async def delete_course_content(content_id: UUID, db: AsyncSession):
    result = await db.execute(select(CourseContent).where(CourseContent.content_id == content_id))
    content = result.scalar_one_or_none()
    if not content:
        return False
    await db.delete(content)
    await db.commit()
    return True


# --------------------
# Lesson Progress Services
# --------------------

async def mark_lesson_complete(user_id: UUID, lesson_id: UUID, db: AsyncSession):
    """Mark a lesson as completed for a user. Creates or updates UserLessonProgress record."""
    # Check if lesson exists
    lesson = await get_lesson_by_id(lesson_id, db)
    if not lesson:
        raise HTTPException(status_code=404, detail=ErrorCode.LESSON_NOT_FOUND)

    course_id = (await db.execute(select(CourseContent.course_id).where(CourseContent.lesson_id == lesson_id))).scalar_one_or_none()
    print(f"Course ID: {course_id}")
    # Check if progress record already exists
    result = await db.execute(
        select(UserLessonProgress).where(
            UserLessonProgress.user_id == user_id,
            UserLessonProgress.lesson_id == lesson_id,
            UserLessonProgress.completed == True,
            UserLessonProgress.viewed_at == datetime.now(),
            UserLessonProgress.course_id == course_id
        )
    )
    progress = result.scalar_one_or_none()

    if progress:
        # Update existing record
        progress.completed = True
        progress.viewed_at = datetime.now(timezone.utc)
    elif progress and progress.completed == False:
        return progress
    else:
        # Create new progress record
        progress = UserLessonProgress(
            user_id=user_id,
            lesson_id=lesson_id,
            course_id=course_id,
            viewed_at=datetime.now(timezone.utc),
            completed=True
        )
        db.add(progress)

    await db.commit()
    await db.refresh(progress)
    return progress


async def get_completed_lessons_for_course(user_id: UUID, course_id: UUID, db: AsyncSession):
    """Get all completed lessons for a user in a specific course."""
    # First verify the course exists
    course = await get_course_by_id(course_id, db)
    if not course:
        raise HTTPException(status_code=404, detail=ErrorCode.COURSE_NOT_FOUND)

    # Get completed lessons for this course
    result = await db.execute(
        select(UserLessonProgress)
        .options(selectinload(UserLessonProgress.lesson))
        .join(CourseContent, CourseContent.lesson_id == UserLessonProgress.lesson_id)
        .where(
            UserLessonProgress.user_id == user_id,
            UserLessonProgress.completed == True,
            CourseContent.course_id == course_id
        )
    )
    return result.scalars().all()

async def update_lesson_progress(user_id: UUID, progress_id: UUID, payload: LessonProgressUpdate, db: AsyncSession):
    """Update the viewed_at timestamp for a lesson."""
    result = await db.execute(
        select(UserLessonProgress).where(
            UserLessonProgress.progress_id == progress_id,
            UserLessonProgress.user_id == user_id
        )
    )
    progress = result.scalar_one_or_none()

    if not progress:
        raise HTTPException(status_code=404, detail="Progress record not found")

    # Update existing record
    if payload.completed is not None:
        progress.completed = payload.completed

    if payload.completed:
        progress.viewed_at = payload.viewed_at or datetime.now(timezone.utc)

    await db.commit()
    await db.refresh(progress)
    return progress
