from typing import List, Any, Dict
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload, selectinload
from sqlalchemy import func
from uuid import UUID
from datetime import datetime, timezone
from fastapi import HTT<PERSON><PERSON>x<PERSON>

from src.DB.enums import AssessmentType
from src.services.assessments import get_simple_ass_by_id
from src.utils.Error_Handling import ErrorCode
from src.DB.models.assessments import UserSession, Response, SessionProfile, Question, Option, ResultProfile
from src.schemas.assessments import SessionCreate, ResponseSubmit, SessionResult, ResultProfileRead, GradingService


async def calculate_result_profile(assessment_id: UUID ,responses: List[Response],  db: AsyncSession):
    assessment = await get_simple_ass_by_id(assessment_id, db)

    if assessment.code == AssessmentType.MBTI:
        return await assign_mbti_profile(assessment_id, responses, db)
    elif assessment.code == AssessmentType.DISC:
        pass
    elif assessment.code == AssessmentType.HEXACO:
        return await assign_hexaco_profile(assessment_id, responses, db)
    elif assessment.code == AssessmentType.PF16:
        pass
    elif assessment.code == AssessmentType.KEIRSEY:
        pass
    elif assessment.code == AssessmentType.RORSCHACH:
        pass
    return None



async def assign_mbti_profile(
    assessment_id: UUID,
    responses: List[Response],
    db: AsyncSession
) -> tuple[Any | None, int]:
    # Step 1: Score MBTI result
    scores = {
        "I": 0, "E": 0,
        "S": 0, "N": 0,
        "T": 0, "F": 0,
        "J": 0, "P": 0
    }

    for response in responses:
        value = (response.value or "").strip().upper()
        if value in scores:
            scores[value] += 1

    mbti_code = ""
    mbti_code += "I" if scores["I"] > scores["E"] else "E"
    mbti_code += "S" if scores["S"] > scores["N"] else "N"
    mbti_code += "T" if scores["T"] > scores["F"] else "F"
    mbti_code += "J" if scores["J"] > scores["P"] else "P"

    # Step 2: Fetch matching ResultProfile
    result_query = await db.execute(
        select(ResultProfile)
        .where(ResultProfile.assessment_id == assessment_id)
        .where(ResultProfile.code == mbti_code)
    )
    result_profile = result_query.scalar_one_or_none()

    if not result_profile:
        raise ValueError(f"No ResultProfile found for MBTI type '{mbti_code}' and assessment {assessment_id}")

    confidence = 100

    return result_profile, confidence


async def assign_hexaco_profile(
    assessment_id: UUID,
    responses: List[Response],
    db: AsyncSession
) -> tuple[Any | None, int]:
    # 1. Fetch all questions with dimension relationship
    question_ids = [r.question_id for r in responses]
    questions_query = await db.execute(
        select(Question).where(Question.question_id.in_(question_ids))
    )
    questions = questions_query.scalars().all()
    question_map = {q.question_id: q for q in questions}

    # 2. Prepare trait score bins
    trait_scores = {
        "H": [],
        "E": [],
        "X": [],
        "A": [],
        "C": [],
        "O": [],
    }

    # 3. Aggregate response values by dimension code
    for response in responses:
        question = question_map.get(response.question_id)
        if not question or not question.dimension:
            continue

        dimension_code = (question.dimension.code or "").upper()
        try:
            value = int(response.value)
        except (TypeError, ValueError):
            continue

        if dimension_code in trait_scores:
            trait_scores[dimension_code].append(value)

    # 4. Average per trait
    trait_averages = {
        trait: (sum(scores) / len(scores)) if scores else 0
        for trait, scores in trait_scores.items()
    }

    # 5. Dominant trait = highest average
    dominant_trait = max(trait_averages.items(), key=lambda x: x[1])[0]
    print(dominant_trait)

    # 6. Fetch matching result profile
    result_query = await db.execute(
        select(ResultProfile)
        .where(ResultProfile.assessment_id == assessment_id)
        .where(ResultProfile.code == dominant_trait)
    )
    result_profile = result_query.scalar_one_or_none()

    if not result_profile:
        raise ValueError(f"ResultProfile not found for HEXACO trait '{dominant_trait}'")

    raw_confidence = (trait_averages[dominant_trait] / 5.0) * 100
    print("Raw confidence (before int cast):", raw_confidence)
    confidence = int(raw_confidence)
    print("Trait averages:", trait_averages)
    print("Dominant trait:", dominant_trait)
    print("Dominant trait value:", trait_averages[dominant_trait])

    print(f'the Result is: result_profile:{result_profile}, confidence: {confidence}')
    return result_profile, confidence





async def grade_session_by_options(session_id: UUID, db: AsyncSession) -> GradingService:
    """
    total_questions    -> # of distinct questions answered in this session
    correct_count      -> count of selected options with is_correct=True
    points_scored      -> sum of selected Option.value
    points_max         -> sum of per-question max(Option.value)
    percent_correct    -> correct_count / total_questions * 100
    """
    # Inner join to ensure invalid option_ids are impossible here
    # (we already validate on submit; this is extra safety)
    result = await db.execute(
        select(
            Response.question_id,
            Option.is_correct,
            Option.value
        )
        .join(Option, Option.option_id == Response.option_id)  # inner join
        .where(Response.session_id == session_id)
    )
    rows = result.all()  # [(question_id, is_correct, value), ...]

    # Distinct questions answered
    qids = [qid for (qid, *_rest) in rows]
    unique_qids = list(dict.fromkeys(qids))  # preserve order
    total_questions = len(unique_qids)

    correct_count = sum(1 for (_qid, is_correct, _pv) in rows if bool(is_correct))
    points_scored = sum(int(pv or 0) for (_qid, _is_correct, pv) in rows)

    points_max = 0
    if unique_qids:
        max_res = await db.execute(
            select(
                Option.question_id,
                func.max(Option.value)
            )
            .where(Option.question_id.in_(unique_qids))
            .group_by(Option.question_id)
        )
        max_map: Dict[UUID, int | None] = dict(max_res.all())
        points_max = sum(int(max_map.get(qid) or 0) for qid in unique_qids)

    percent_correct = (correct_count / total_questions * 100.0) if total_questions else 0.0

    return GradingService(
        total_questions=total_questions,
        correct_count=correct_count,
        percent_correct=percent_correct,
        points_scored=points_scored,
        points_max=points_max,
    )

